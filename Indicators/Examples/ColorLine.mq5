//+------------------------------------------------------------------+
//|                                                    ColorLine.mq5 |
//|                             Copyright 2000-2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2000-2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   1
//--- plot ColorLine
#property indicator_label1  "ColorLine"
#property indicator_type1   DRAW_COLOR_LINE
#property indicator_color1  clrRed,clrGreen,clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  3
//--- indicator buffers
double ExtColorLineBuffer[];
double ExtColorsBuffer[];

int    ExtMAHandle;
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
void OnInit()
  {
//--- indicator buffers mapping
   SetIndexBuffer(0,ExtColorLineBuffer,INDICATOR_DATA);
   SetIndexBuffer(1,ExtColorsBuffer,INDICATOR_COLOR_INDEX);
//--- get MA handle
   ExtMAHandle=iMA(Symbol(),0,10,0,MODE_EMA,PRICE_CLOSE);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   static int ticks=0,modified=0;
//--- check data
   int calculated=BarsCalculated(ExtMAHandle);
   if(calculated<rates_total)
     {
      Print("Not all data of ExtMAHandle is calculated (",calculated," bars). Error ",GetLastError());
      return(0);
     }
//--- first calculation or number of bars was changed
   if(prev_calculated==0)
     {
      //--- copy values of MA into indicator buffer ExtColorLineBuffer
      if(CopyBuffer(ExtMAHandle,0,0,rates_total,ExtColorLineBuffer)<=0)
         return(0);
      //--- now set line color for every bar
      for(int i=0; i<rates_total && !IsStopped(); i++)
         ExtColorsBuffer[i]=getIndexOfColor(i);
     }
   else
     {
      //--- we can copy not all data
      int to_copy;
      if(prev_calculated>rates_total || prev_calculated<0)
         to_copy=rates_total;
      else
        {
         to_copy=rates_total-prev_calculated;
         if(prev_calculated>0)
            to_copy++;
        }
      //--- copy values of MA into indicator buffer ExtColorLineBuffer
      int copied=CopyBuffer(ExtMAHandle,0,0,rates_total,ExtColorLineBuffer);
      if(copied<=0)
         return(0);

      ticks++; // ticks counting
      if(ticks>=5) //it's time to change color scheme
        {
         ticks=0; // reset counter
         modified++; // counter of color changes
         if(modified>=3)
            modified=0;// reset counter
         ResetLastError();
         switch(modified)
           {
            case 0: // first color scheme
               PlotIndexSetInteger(0,PLOT_LINE_COLOR,0,clrRed);
               PlotIndexSetInteger(0,PLOT_LINE_COLOR,1,clrBlue);
               PlotIndexSetInteger(0,PLOT_LINE_COLOR,2,clrGreen);
               break;
            case 1: // second color scheme
               PlotIndexSetInteger(0,PLOT_LINE_COLOR,0,clrYellow);
               PlotIndexSetInteger(0,PLOT_LINE_COLOR,1,clrPink);
               PlotIndexSetInteger(0,PLOT_LINE_COLOR,2,clrLightSlateGray);
               break;
            default: // third color scheme
               PlotIndexSetInteger(0,PLOT_LINE_COLOR,0,clrLightGoldenrod);
               PlotIndexSetInteger(0,PLOT_LINE_COLOR,1,clrOrchid);
               PlotIndexSetInteger(0,PLOT_LINE_COLOR,2,clrLimeGreen);
           }
        }
      else
        {
         //--- set start position
         int start=prev_calculated-1;
         //--- now we set line color for every bar
         for(int i=start; i<rates_total && !IsStopped(); i++)
            ExtColorsBuffer[i]=getIndexOfColor(i);
        }
     }
//--- return value of prev_calculated for next call
   return(rates_total);
  }
//+------------------------------------------------------------------+
//| get color index                                                  |
//+------------------------------------------------------------------+
int getIndexOfColor(const int i)
  {
   int j=i%300;
   if(j<100)     // first index
      return(0);
   if(j<200)     // second index
      return(1);
   return(2);    // third index
  }
//+------------------------------------------------------------------+
