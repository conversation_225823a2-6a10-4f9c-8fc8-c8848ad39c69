//+------------------------------------------------------------------+
//|                                        DivergenceSignalIndicator.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots   0

//--- Input parameters
input int      min_target = 7;           // Minimum target in pips
input int      max_target = 20;          // Maximum target in pips
input int      TSL1 = 5;                 // First trailing stop level in pips
input int      TSL2 = 10;                // Second trailing stop level in pips
input bool     limite_hours = false;     // Limit trading hours
input string   trade_hours_start = "10:00"; // Trading start time
input string   trade_hours_stop = "22:00";  // Trading stop time

//--- Global variables
int wma4_handle_h1, wma8_handle_h1, wma16_handle_h1;
int wma50_handle_m1;
int rsi_handle_m1, macd_handle_m1, stoch_handle_m1;

double wma4_h1[], wma8_h1[], wma16_h1[];
double wma50_m1[];
double rsi_m1[], macd_main[], macd_signal[], stoch_main[], stoch_signal[];

string prefix = "DivSignal_";
int lookback_bars = 2880; // 2 days * 24 hours * 60 minutes

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize indicator handles for 1H timeframe
    wma4_handle_h1 = iMA(_Symbol, PERIOD_H1, 4, 0, MODE_LWMA, PRICE_CLOSE);
    wma8_handle_h1 = iMA(_Symbol, PERIOD_H1, 8, 0, MODE_LWMA, PRICE_CLOSE);
    wma16_handle_h1 = iMA(_Symbol, PERIOD_H1, 16, 0, MODE_LWMA, PRICE_CLOSE);
    
    //--- Initialize indicator handles for 1M timeframe
    wma50_handle_m1 = iMA(_Symbol, PERIOD_M1, 50, 0, MODE_LWMA, PRICE_CLOSE);
    rsi_handle_m1 = iRSI(_Symbol, PERIOD_M1, 7, PRICE_CLOSE);
    macd_handle_m1 = iMACD(_Symbol, PERIOD_M1, 12, 26, 9, PRICE_CLOSE);
    stoch_handle_m1 = iStochastic(_Symbol, PERIOD_M1, 5, 3, 3, MODE_SMA, STO_LOWHIGH);
    
    //--- Check if handles are valid
    if(wma4_handle_h1 == INVALID_HANDLE || wma8_handle_h1 == INVALID_HANDLE || 
       wma16_handle_h1 == INVALID_HANDLE || wma50_handle_m1 == INVALID_HANDLE ||
       rsi_handle_m1 == INVALID_HANDLE || macd_handle_m1 == INVALID_HANDLE ||
       stoch_handle_m1 == INVALID_HANDLE)
    {
        Print("Error creating indicator handles");
        return(INIT_FAILED);
    }
    
    //--- Resize arrays
    ArraySetAsSeries(wma4_h1, true);
    ArraySetAsSeries(wma8_h1, true);
    ArraySetAsSeries(wma16_h1, true);
    ArraySetAsSeries(wma50_m1, true);
    ArraySetAsSeries(rsi_m1, true);
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);
    ArraySetAsSeries(stoch_main, true);
    ArraySetAsSeries(stoch_signal, true);
    
    //--- Analyze previous 2 days when indicator is added
    AnalyzePreviousDays();
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Remove all objects created by this indicator
    RemoveAllObjects();
    
    //--- Release indicator handles
    if(wma4_handle_h1 != INVALID_HANDLE) IndicatorRelease(wma4_handle_h1);
    if(wma8_handle_h1 != INVALID_HANDLE) IndicatorRelease(wma8_handle_h1);
    if(wma16_handle_h1 != INVALID_HANDLE) IndicatorRelease(wma16_handle_h1);
    if(wma50_handle_m1 != INVALID_HANDLE) IndicatorRelease(wma50_handle_m1);
    if(rsi_handle_m1 != INVALID_HANDLE) IndicatorRelease(rsi_handle_m1);
    if(macd_handle_m1 != INVALID_HANDLE) IndicatorRelease(macd_handle_m1);
    if(stoch_handle_m1 != INVALID_HANDLE) IndicatorRelease(stoch_handle_m1);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    //--- Check for new tick and analyze current conditions
    static datetime last_time = 0;
    if(time[rates_total-1] != last_time)
    {
        last_time = time[rates_total-1];
        CheckForSignals();
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Remove all objects created by this indicator                     |
//+------------------------------------------------------------------+
void RemoveAllObjects()
{
    // OBJ_ALL is not available in MQL5, use OBJ_ARROW, OBJ_TREND, OBJ_TEXT, etc.
    int total = ObjectsTotal(0); // Remove the type filter
    for(int i = total - 1; i >= 0; i--)
    {
        string name = ObjectName(0, i);
        if(StringFind(name, prefix) == 0)
        {
            ObjectDelete(0, name);
        }
    }
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Analyze previous 2 days for signals                             |
//+------------------------------------------------------------------+
void AnalyzePreviousDays()
{
    Print("Analyzing previous 2 days for signals...");

    for(int i = 1; i < lookback_bars; i++)
    {
        if(CheckSignalConditions(i))
        {
            datetime signal_time = iTime(_Symbol, PERIOD_M1, i);
            double signal_price = iClose(_Symbol, PERIOD_M1, i);

            // Determine signal type
            int signal_type = GetSignalType(i);
            if(signal_type != 0)
            {
                DrawSignalArrow(signal_time, signal_price, signal_type, i);
                DrawDivergenceLines(i, signal_type);
            }
        }
    }
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Check for new signals on current tick                           |
//+------------------------------------------------------------------+
void CheckForSignals()
{
    // Check if trading hours are limited
    if(limite_hours && !IsWithinTradingHours())
        return;

    // Check current bar for signal conditions
    if(CheckSignalConditions(0))
    {
        datetime signal_time = TimeCurrent();
        double signal_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

        int signal_type = GetSignalType(0);
        if(signal_type != 0)
        {
            DrawSignalArrow(signal_time, signal_price, signal_type, 0);
            DrawDivergenceLines(0, signal_type);

            // Send alert
            string alert_msg = StringFormat("Divergence Signal: %s on %s",
                                           (signal_type > 0) ? "BUY" : "SELL", _Symbol);
            Alert(alert_msg);
            Print(alert_msg);
        }
    }
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                   |
//+------------------------------------------------------------------+
bool IsWithinTradingHours()
{
    if(!limite_hours) return true;

    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    int current_minutes = dt.hour * 60 + dt.min;

    // Parse start and stop times
    string start_parts[];
    string stop_parts[];
    StringSplit(trade_hours_start, ':', start_parts);
    StringSplit(trade_hours_stop, ':', stop_parts);

    int start_minutes = (int)start_parts[0] * 60 + (int)start_parts[1];
    int stop_minutes = (int)stop_parts[0] * 60 + (int)stop_parts[1];

    if(start_minutes <= stop_minutes)
    {
        return (current_minutes >= start_minutes && current_minutes <= stop_minutes);
    }
    else
    {
        return (current_minutes >= start_minutes || current_minutes <= stop_minutes);
    }
}

//+------------------------------------------------------------------+
//| Check signal conditions for given bar                           |
//+------------------------------------------------------------------+
bool CheckSignalConditions(int bar_index)
{
    // Get 1H timeframe data for trend direction
    if(!GetH1TrendDirection(bar_index))
        return false;

    // Check for multiple tops/bottoms
    if(!HasMultipleTopsBottoms(bar_index))
        return false;

    // Check WMA50 cross
    if(!HasWMA50Cross(bar_index))
        return false;

    // Check for divergence
    if(!HasDivergence(bar_index))
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| Get 1H trend direction                                          |
//+------------------------------------------------------------------+
bool GetH1TrendDirection(int bar_index)
{
    // Get corresponding H1 bar
    datetime m1_time = iTime(_Symbol, PERIOD_M1, bar_index);
    int h1_bar = iBarShift(_Symbol, PERIOD_H1, m1_time);

    // Get WMA values
    if(CopyBuffer(wma8_handle_h1, 0, h1_bar, 2, wma8_h1) < 2 ||
       CopyBuffer(wma16_handle_h1, 0, h1_bar, 2, wma16_h1) < 2)
        return false;

    // Check trend direction: WMA8 > WMA16 = Buy zone, WMA8 < WMA16 = Sell zone
    return (MathAbs(wma8_h1[0] - wma16_h1[0]) > Point() * 2); // Minimum 2 points difference
}

//+------------------------------------------------------------------+
//| Check for multiple tops/bottoms                                 |
//+------------------------------------------------------------------+
bool HasMultipleTopsBottoms(int bar_index)
{
    int lookback = 20; // Look back 20 bars for tops/bottoms
    double highs[], lows[];

    if(CopyHigh(_Symbol, PERIOD_M1, bar_index, lookback, highs) < lookback ||
       CopyLow(_Symbol, PERIOD_M1, bar_index, lookback, lows) < lookback)
        return false;

    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);

    // Count significant tops and bottoms
    int tops = 0, bottoms = 0;
    double atr = GetATR(bar_index, 14);

    for(int i = 2; i < lookback - 2; i++)
    {
        // Check for top
        if(highs[i] > highs[i-1] && highs[i] > highs[i+1] &&
           highs[i] > highs[i-2] && highs[i] > highs[i+2])
        {
            tops++;
        }

        // Check for bottom
        if(lows[i] < lows[i-1] && lows[i] < lows[i+1] &&
           lows[i] < lows[i-2] && lows[i] < lows[i+2])
        {
            bottoms++;
        }
    }

    return (tops >= 2 || bottoms >= 2);
}

//+------------------------------------------------------------------+
//| Check WMA50 cross                                               |
//+------------------------------------------------------------------+
bool HasWMA50Cross(int bar_index)
{
    if(CopyBuffer(wma50_handle_m1, 0, bar_index, 3, wma50_m1) < 3)
        return false;

    double close_prices[];
    if(CopyClose(_Symbol, PERIOD_M1, bar_index, 3, close_prices) < 3)
        return false;

    ArraySetAsSeries(close_prices, true);

    // Check if price crossed and closed above/below WMA50
    bool cross_up = (close_prices[1] <= wma50_m1[1] && close_prices[0] > wma50_m1[0]);
    bool cross_down = (close_prices[1] >= wma50_m1[1] && close_prices[0] < wma50_m1[0]);

    return (cross_up || cross_down);
}

//+------------------------------------------------------------------+
//| Check for divergence in RSI, MACD, and Stochastic             |
//+------------------------------------------------------------------+
bool HasDivergence(int bar_index)
{
    int lookback = 20;

    // Get indicator values
    if(CopyBuffer(rsi_handle_m1, 0, bar_index, lookback, rsi_m1) < lookback ||
       CopyBuffer(macd_handle_m1, 0, bar_index, lookback, macd_main) < lookback ||
       CopyBuffer(stoch_handle_m1, 0, bar_index, lookback, stoch_main) < lookback)
        return false;

    double highs[], lows[];
    if(CopyHigh(_Symbol, PERIOD_M1, bar_index, lookback, highs) < lookback ||
       CopyLow(_Symbol, PERIOD_M1, bar_index, lookback, lows) < lookback)
        return false;

    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);

    // Check for bullish divergence (price makes lower lows, indicators make higher lows)
    bool rsi_bull_div = CheckBullishDivergence(lows, rsi_m1, lookback);
    bool macd_bull_div = CheckBullishDivergence(lows, macd_main, lookback);
    bool stoch_bull_div = CheckBullishDivergence(lows, stoch_main, lookback);

    // Check for bearish divergence (price makes higher highs, indicators make lower highs)
    bool rsi_bear_div = CheckBearishDivergence(highs, rsi_m1, lookback);
    bool macd_bear_div = CheckBearishDivergence(highs, macd_main, lookback);
    bool stoch_bear_div = CheckBearishDivergence(highs, stoch_main, lookback);

    // Require at least 2 indicators to show divergence
    int bull_count = (rsi_bull_div ? 1 : 0) + (macd_bull_div ? 1 : 0) + (stoch_bull_div ? 1 : 0);
    int bear_count = (rsi_bear_div ? 1 : 0) + (macd_bear_div ? 1 : 0) + (stoch_bear_div ? 1 : 0);

    return (bull_count >= 2 || bear_count >= 2);
}

//+------------------------------------------------------------------+
//| Check bullish divergence                                        |
//+------------------------------------------------------------------+
bool CheckBullishDivergence(double &price_array[], double &indicator_array[], int size)
{
    // Find two recent lows in price
    int low1_idx = -1, low2_idx = -1;
    double low1_price = DBL_MAX, low2_price = DBL_MAX;

    for(int i = 2; i < size - 2; i++)
    {
        if(price_array[i] < price_array[i-1] && price_array[i] < price_array[i+1] &&
           price_array[i] < price_array[i-2] && price_array[i] < price_array[i+2])
        {
            if(low1_idx == -1 || price_array[i] < low1_price)
            {
                low2_idx = low1_idx;
                low2_price = low1_price;
                low1_idx = i;
                low1_price = price_array[i];
            }
            else if(low2_idx == -1 || price_array[i] < low2_price)
            {
                low2_idx = i;
                low2_price = price_array[i];
            }
        }
    }

    if(low1_idx == -1 || low2_idx == -1) return false;

    // Check if indicator shows higher low when price shows lower low
    return (low1_price < low2_price && indicator_array[low1_idx] > indicator_array[low2_idx]);
}

//+------------------------------------------------------------------+
//| Check bearish divergence                                        |
//+------------------------------------------------------------------+
bool CheckBearishDivergence(double &price_array[], double &indicator_array[], int size)
{
    // Find two recent highs in price
    int high1_idx = -1, high2_idx = -1;
    double high1_price = -DBL_MAX, high2_price = -DBL_MAX;

    for(int i = 2; i < size - 2; i++)
    {
        if(price_array[i] > price_array[i-1] && price_array[i] > price_array[i+1] &&
           price_array[i] > price_array[i-2] && price_array[i] > price_array[i+2])
        {
            if(high1_idx == -1 || price_array[i] > high1_price)
            {
                high2_idx = high1_idx;
                high2_price = high1_price;
                high1_idx = i;
                high1_price = price_array[i];
            }
            else if(high2_idx == -1 || price_array[i] > high2_price)
            {
                high2_idx = i;
                high2_price = price_array[i];
            }
        }
    }

    if(high1_idx == -1 || high2_idx == -1) return false;

    // Check if indicator shows lower high when price shows higher high
    return (high1_price > high2_price && indicator_array[high1_idx] < indicator_array[high2_idx]);
}

//+------------------------------------------------------------------+
//| Get signal type (1 = Buy, -1 = Sell, 0 = No signal)           |
//+------------------------------------------------------------------+
int GetSignalType(int bar_index)
{
    // Get H1 trend direction
    datetime m1_time = iTime(_Symbol, PERIOD_M1, bar_index);
    int h1_bar = iBarShift(_Symbol, PERIOD_H1, m1_time);

    if(CopyBuffer(wma8_handle_h1, 0, h1_bar, 1, wma8_h1) < 1 ||
       CopyBuffer(wma16_handle_h1, 0, h1_bar, 1, wma16_h1) < 1)
        return 0;

    // Get WMA50 cross direction
    if(CopyBuffer(wma50_handle_m1, 0, bar_index, 2, wma50_m1) < 2)
        return 0;

    double close_prices[];
    if(CopyClose(_Symbol, PERIOD_M1, bar_index, 2, close_prices) < 2)
        return 0;

    ArraySetAsSeries(close_prices, true);

    bool cross_up = (close_prices[1] <= wma50_m1[1] && close_prices[0] > wma50_m1[0]);
    bool cross_down = (close_prices[1] >= wma50_m1[1] && close_prices[0] < wma50_m1[0]);

    // Buy signal: H1 uptrend + M1 cross up + bullish divergence
    if(wma8_h1[0] > wma16_h1[0] && cross_up)
        return 1;

    // Sell signal: H1 downtrend + M1 cross down + bearish divergence
    if(wma8_h1[0] < wma16_h1[0] && cross_down)
        return -1;

    return 0;
}

//+------------------------------------------------------------------+
//| Draw signal arrow on chart                                      |
//+------------------------------------------------------------------+
void DrawSignalArrow(datetime time, double price, int signal_type, int bar_index)
{
    string name = prefix + "Arrow_" + IntegerToString(bar_index) + "_" + TimeToString(time);

    if(ObjectFind(0, name) >= 0)
        ObjectDelete(0, name);

    if(signal_type > 0) // Buy signal
    {
        ObjectCreate(0, name, OBJ_ARROW_UP, 0, time, price - 10 * Point());
        ObjectSetInteger(0, name, OBJPROP_COLOR, clrLime);
        ObjectSetInteger(0, name, OBJPROP_WIDTH, 3);
    }
    else if(signal_type < 0) // Sell signal
    {
        ObjectCreate(0, name, OBJ_ARROW_DOWN, 0, time, price + 10 * Point());
        ObjectSetInteger(0, name, OBJPROP_COLOR, clrRed);
        ObjectSetInteger(0, name, OBJPROP_WIDTH, 3);
    }

    ObjectSetString(0, name, OBJPROP_TEXT, "Divergence Signal");
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| Draw divergence lines                                           |
//+------------------------------------------------------------------+
void DrawDivergenceLines(int bar_index, int signal_type)
{
    int lookback = 20;

    // Get price and indicator data
    double highs[], lows[], rsi_values[], macd_values[], stoch_values[];

    if(CopyHigh(_Symbol, PERIOD_M1, bar_index, lookback, highs) < lookback ||
       CopyLow(_Symbol, PERIOD_M1, bar_index, lookback, lows) < lookback ||
       CopyBuffer(rsi_handle_m1, 0, bar_index, lookback, rsi_values) < lookback ||
       CopyBuffer(macd_handle_m1, 0, bar_index, lookback, macd_values) < lookback ||
       CopyBuffer(stoch_handle_m1, 0, bar_index, lookback, stoch_values) < lookback)
        return;

    ArraySetAsSeries(highs, true);
    ArraySetAsSeries(lows, true);
    ArraySetAsSeries(rsi_values, true);
    ArraySetAsSeries(macd_values, true);
    ArraySetAsSeries(stoch_values, true);

    if(signal_type > 0) // Bullish divergence
    {
        DrawBullishDivergenceLines(bar_index, lows, rsi_values, "RSI");
        DrawBullishDivergenceLines(bar_index, lows, macd_values, "MACD");
        DrawBullishDivergenceLines(bar_index, lows, stoch_values, "STOCH");
    }
    else if(signal_type < 0) // Bearish divergence
    {
        DrawBearishDivergenceLines(bar_index, highs, rsi_values, "RSI");
        DrawBearishDivergenceLines(bar_index, highs, macd_values, "MACD");
        DrawBearishDivergenceLines(bar_index, highs, stoch_values, "STOCH");
    }
}

//+------------------------------------------------------------------+
//| Draw bullish divergence lines                                   |
//+------------------------------------------------------------------+
void DrawBullishDivergenceLines(int bar_index, double &price_array[], double &indicator_array[], string indicator_name)
{
    // Find two lows for divergence
    int low1_idx = -1, low2_idx = -1;
    double low1_price = DBL_MAX, low2_price = DBL_MAX;

    for(int i = 2; i < 18; i++) // Look back 18 bars
    {
        if(price_array[i] < price_array[i-1] && price_array[i] < price_array[i+1] &&
           price_array[i] < price_array[i-2] && price_array[i] < price_array[i+2])
        {
            if(low1_idx == -1 || price_array[i] < low1_price)
            {
                low2_idx = low1_idx;
                low2_price = low1_price;
                low1_idx = i;
                low1_price = price_array[i];
            }
            else if(low2_idx == -1 || price_array[i] < low2_price)
            {
                low2_idx = i;
                low2_price = price_array[i];
            }
        }
    }

    if(low1_idx == -1 || low2_idx == -1) return;
    if(!(low1_price < low2_price && indicator_array[low1_idx] > indicator_array[low2_idx])) return;

    // Draw line
    datetime time1 = iTime(_Symbol, PERIOD_M1, bar_index + low1_idx);
    datetime time2 = iTime(_Symbol, PERIOD_M1, bar_index + low2_idx);

    string name = prefix + "DivLine_" + indicator_name + "_" + IntegerToString(bar_index) + "_" + TimeToString(time1);

    ObjectCreate(0, name, OBJ_TREND, 0, time1, low1_price, time2, low2_price);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clrBlue);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DASH);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| Draw bearish divergence lines                                   |
//+------------------------------------------------------------------+
void DrawBearishDivergenceLines(int bar_index, double &price_array[], double &indicator_array[], string indicator_name)
{
    // Find two highs for divergence
    int high1_idx = -1, high2_idx = -1;
    double high1_price = -DBL_MAX, high2_price = -DBL_MAX;

    for(int i = 2; i < 18; i++) // Look back 18 bars
    {
        if(price_array[i] > price_array[i-1] && price_array[i] > price_array[i+1] &&
           price_array[i] > price_array[i-2] && price_array[i] > price_array[i+2])
        {
            if(high1_idx == -1 || price_array[i] > high1_price)
            {
                high2_idx = high1_idx;
                high2_price = high1_price;
                high1_idx = i;
                high1_price = price_array[i];
            }
            else if(high2_idx == -1 || price_array[i] > high2_price)
            {
                high2_idx = i;
                high2_price = price_array[i];
            }
        }
    }

    if(high1_idx == -1 || high2_idx == -1) return;
    if(!(high1_price > high2_price && indicator_array[high1_idx] < indicator_array[high2_idx])) return;

    // Draw line
    datetime time1 = iTime(_Symbol, PERIOD_M1, bar_index + high1_idx);
    datetime time2 = iTime(_Symbol, PERIOD_M1, bar_index + high2_idx);

    string name = prefix + "DivLine_" + indicator_name + "_" + IntegerToString(bar_index) + "_" + TimeToString(time1);

    ObjectCreate(0, name, OBJ_TREND, 0, time1, high1_price, time2, high2_price);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clrOrange);
    ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_DASH);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| Get Average True Range for volatility measurement               |
//+------------------------------------------------------------------+
double GetATR(int bar_index, int period)
{
    int atr_handle = iATR(_Symbol, PERIOD_M1, period);
    if(atr_handle == INVALID_HANDLE) return 0.0;

    double atr_values[];
    if(CopyBuffer(atr_handle, 0, bar_index, 1, atr_values) < 1)
    {
        IndicatorRelease(atr_handle);
        return 0.0;
    }

    double result = atr_values[0];
    IndicatorRelease(atr_handle);
    return result;
}
