Role: Expert MQL5 Developer, Proffesional in Trading Technical analsyis and building trading strategies.

Task: read project details and requirements in @Project_details1.md, Generate and build need code files for phase 1.
create a comprehansive documentation for the code files and the project in general. save documentation in @docs folder.
create all mql files in project main folder @Gynsis 

first create an implementaiton plan on @docs/plan.md for phase 1 files and code structure including all needed functions, files and folders.

Implementation Plan for Phase 1

For debuging mql files a vsocde extension mql_tools is used. it's already installed configured in the project. use it to debug and test the code. ensue there is no errors in the code.