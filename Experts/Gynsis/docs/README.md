# Gynsis — Phase 1

A modular multi-strategy Expert Advisor blueprint for MetaTrader 5.

Author: <PERSON>


## Objectives (Phase 1)
- Scaffold the EA with clean modular structure
- Main EA routes events and delegates to strategies
- Panel scaffolding with 5 tabs (placeholders)
- Logger, risk/order helpers, config placeholders
- Two strategy placeholders (50MA_crossing and 1M_8_16)


## Structure
- Gynsis.mq5 — Main EA
- Core/
  - Types.mqh, Globals.mqh, Config.mqh, Logger.mqh, Risk.mqh, Orders.mqh, Storage.mqh, PanelStyles.mqh, Panel.mqh
  - Utils/TA.mqh — technical utilities (basic MA and cross helpers)
  - API/Http.mqh, API/Telegram.mqh — placeholders for future integrations
- Strategies/
  - StrategyBase.mqh — strategy interface
  - MA50Cross.mqh — 50MA crossing (signals only)
  - M1_8_16.mqh — M1 8/16 rules (signals only)
- docs/
  - Project_details1.md, task1.md, plan.md, README.md


## Compile & Run
1) Open Gynsis.mq5 in MetaEditor or VSCode with mql_tools.
2) Compile.
3) Attach to a chart (default symbol becomes the first tracked pair).
4) A panel window will appear with "Experts", "Pairs", "Risk", "Stat", "Market" tabs (placeholders).

Notes:
- Logger writes CSV into MQL5/Files with a date-based filename. If subfolder creation isn’t available, the log path may fall back to root of Files.
- Inputs are grouped and prefixed: sys_*, panel_*, stg_ma50_*, stg_m1_8_16_*.


## Phase 1 Behavior
- On each tick, EA iterates configured pairs (default: current chart symbol) and calls each strategy.
- Strategies compute signals and print to log. No orders are placed in Phase 1.
- Risk/Orders/Config provide callable helpers with safe defaults.


## Next Steps (Phase 2)
- Implement full panel interactivity and persistence
- Complete TA utilities (swings, fibs, divergences)
- Implement order placement with risk-based sizing
- Add HTTP/Telegram integrations and account statistics
