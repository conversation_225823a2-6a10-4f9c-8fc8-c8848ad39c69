[Starting] [13:31:18] Compiling >>> Gynsis.mq5 <<< 


null
'Trade.mqh'
'Object.mqh'
'StdLibErr.mqh'
'OrderInfo.mqh'
'HistoryOrderInfo.mqh'
'PositionInfo.mqh'
'DealInfo.mqh'
'Dialog.mqh'
'WndContainer.mqh'
'Wnd.mqh'
'Rect.mqh'
'Defines.mqh'
'ArrayObj.mqh'
'Array.mqh'
'WndClient.mqh'
'Panel.mqh'
'WndObj.mqh'
'ChartObjectsTxtControls.mqh'
'ChartObject.mqh'
'Scrolls.mqh'
'BmpButton.mqh'
'ChartObjectsBmpControls.mqh'
'Edit.mqh'
'Chart.mqh'
'Types.mqh'
⛔ : '#pragma' - invalid preprocessor command (1,1)
'Globals.mqh'
'Config.mqh'
'Logger.mqh'
'Risk.mqh'
'Orders.mqh'
'PanelStyles.mqh'
⛔ : file 'C:\Program Files\MetaTrader 5\MQL5\Include\Controls\TabControl.mqh' not found (5,11)
'Label.mqh'
'Storage.mqh'
'TA.mqh'
'StrategyBase.mqh'
'MA50Cross.mqh'
'M1_8_16.mqh'
🟡 : version '0.1.0' is incompatible with MQL5 Market, must be xxx.yyy (3,11)
 •    'Up.bmp' as resource "::res\Up.bmp"
 •    'ThumbVert.bmp' as resource "::res\ThumbVert.bmp"
 •    'Down.bmp' as resource "::res\Down.bmp"
 •    'Left.bmp' as resource "::res\Left.bmp"
 •    'ThumbHor.bmp' as resource "::res\ThumbHor.bmp"
 •    'Right.bmp' as resource "::res\Right.bmp"
 •    'Close.bmp' as resource "::res\Close.bmp"
 •    'Restore.bmp' as resource "::res\Restore.bmp"
 •    'Turn.bmp' as resource "::res\Turn.bmp"
⛔ : '#pragma' - expressions are not allowed on a global scope (1,1)
⛔ : 'ENUM_STRATEGY_ID' - unexpected token, probably type is missing? (29,4)
⛔ : 'id' - semicolon expected (29,22)
⛔ : 'CLabel' - struct undefined (39,1)
⛔ : 'CLabel' - unexpected token, probably type is missing? (39,9)
⛔ : 'CLabel' - struct undefined (46,1)
⛔ : '~' - destructor tag mismatch (46,9)
⛔ : 'CLabel' - struct undefined (52,6)
⛔ : 'CLabel' - struct undefined (66,6)
⛔ : 'CLabel' - struct undefined (74,6)
⛔ : 'CLabel' - struct undefined (81,6)
⛔ : 'CLabel' - struct undefined (88,6)
⛔ : 'CTabControl' - unexpected token, probably type is missing? (15,4)
⛔ : 'm_tabs' - semicolon expected (15,17)
⛔ : 'CLabel' - unexpected token, probably type is missing? (17,4)
⛔ : 'm_title' - semicolon expected (17,17)
⛔ : 'PanelStyle' - unexpected token, probably type is missing? (18,4)
⛔ : 'm_style' - semicolon expected (18,17)
⛔ : 'Logger' - unexpected token, probably type is missing? (25,1)
⛔ : 'g_logger' - semicolon expected (25,17)
⛔ : 'StrategyBase' - unexpected token, probably type is missing? (27,1)
⛔ : '*' - semicolon expected (27,16)
⛔ : declaration without type (34,44)
⛔ : '*' - comma expected (34,57)
⛔ : declaration without type (35,20)
⛔ : 'id' - comma expected (35,37)
⛔ : declaration without type (140,44)
⛔ : '*' - comma expected (140,57)
⛔ : declaration without type (157,20)
⛔ : 'id' - comma expected (157,37)
⛔ : undeclared identifier (43,4)
⛔ : undeclared identifier (44,4)
⛔ : 'Config' is not a class, struct or union (66,4)
⛔ : undeclared identifier (66,12)
⛔ : 'Save' - some operator expected (66,12)
⛔ : ')' - unexpected token (66,28)
🟡 : expression has no effect (66,17)
⛔ : undeclared identifier (69,4)
⛔ : undeclared identifier (70,4)
⛔ : undeclared identifier (37,7)
⛔ : undeclared identifier (37,13)
⛔ : '0' - some operator expected (37,30)
⛔ : undeclared identifier (41,4)
⛔ : 'Create' - access to non-static member or function (55,17)
 •      see declaration of function 'CWnd::Create'
⛔ : undeclared identifier (58,8)
⛔ : ';' - unexpected token (59,20)
⛔ : '(' - unbalanced left parenthesis (58,6)
🟡 : empty controlled statement found (59,20)
⛔ : undeclared identifier (61,11)
⛔ : ')' - expression expected (61,20)
🟡 : expression not boolean (61,11)
⛔ : undeclared identifier (69,11)
⛔ : ';' - unexpected token (69,77)
⛔ : '}' - semicolon expected (70,3)
🟡 : expression not boolean (69,11)
⛔ : undeclared identifier (76,11)
⛔ : ';' - unexpected token (76,47)
⛔ : '}' - semicolon expected (77,3)
🟡 : expression not boolean (76,11)
⛔ : undeclared identifier (83,11)
⛔ : ';' - unexpected token (83,46)
⛔ : '}' - semicolon expected (84,3)
🟡 : expression not boolean (83,11)
⛔ : undeclared identifier (91,11)
⛔ : undeclared identifier (91,46)
⛔ : ';' - unexpected token (91,77)
⛔ : '}' - semicolon expected (92,3)
⛔ : undeclared identifier (25,7)
⛔ : wrong parameters count, 6 passed, but 7 requires (26,17)
 •      bool CAppDialog::Create(const long,const string,const int,const int,const int,const int,const int)
⛔ : undeclared identifier (28,13)
⛔ : 'clrNONE' - some operator expected (28,29)
⛔ : wrong parameters count, 5 passed, but 7 requires (31,18)
 •      bool CPanel::Create(const long,const string,const int,const int,const int,const int,const int)
⛔ : undeclared identifier (33,30)
⛔ : ';' - unexpected token (33,47)

[Error] Result: 100 errors, 7 warnings