# Gynsis — Phase 1 Implementation Plan

Role: Expert MQL5 Developer

Objective: Establish a clean, modular expert advisor blueprint with placeholders and minimal working logic that compiles without errors, ready to expand in later phases.

Key outcomes in Phase 1:
- Main expert file orchestrating platform events and delegating to modules
- Strategy framework supporting multiple strategies
- Risk and order management scaffolding
- System utilities for technical analysis (stubs)
- Dashboard panel scaffolding with tabs (placeholders using MQL5 Controls)
- Logging and persistent configuration scaffolding
- Placeholders for external API and Telegram integrations
- Organized inputs naming and grouping


## 1) Project Structure

Experts/Gynsis/
- Gynsis.mq5 — Main expert entry point (OnInit/OnTick/OnDeinit, module wiring)
- docs/
  - Project_details1.md
  - task1.md
  - plan.md
  - README.md (to be generated with overview and usage)
- Core/
  - Types.mqh — Common enums/structs/constants, magic numbers, error codes
  - Globals.mqh — Input parameters for the EA, grouped and prefixed
  - Config.mqh — System and runtime configuration container + load/save placeholders
  - Logger.mqh — Lightweight logging to file wrapper
  - Risk.mqh — Risk management helpers: position sizing, accumulated risk computation (stubs)
  - Orders.mqh — CTrade wrapper: open/close helpers with sl/tp, magic tagging (stubs)
  - Storage.mqh — Persistence helpers (files + (placeholder) globals)
  - PanelStyles.mqh — UI style variables for the panel
  - Panel.mqh — Dashboard panel scaffolding using <Controls/> components, multiple tabs
  - Utils/
    - TA.mqh — Technical analysis utilities (high/low, swings, fibs, PA patterns, divergence stubs)
  - API/
    - Http.mqh — HTTP client placeholders
    - Telegram.mqh — Telegram integration placeholders
- Strategies/
  - StrategyBase.mqh — Base abstract interface for strategies
  - MA50Cross.mqh — Strategy B.1 implementation placeholder
  - M1_8_16.mqh — Strategy B.2 implementation placeholder

Notes:
- All .mqh files are included by Gynsis.mq5. Strategies are instantiated in a registry (array of pointers/objects) and iterated for enabled pairs.
- Use #property strict; avoid dynamic allocations where possible; use static arrays or CArrayObj with care.


## 2) Inputs Organization (Globals.mqh)
- Prefix by component for clarity and MT5 optimization usage
  - General: sys_* (e.g., sys_risk_pct_default, sys_max_open_trades, sys_max_pairs)
  - Panel: panel_* (colors, transparency, sizes)
  - Strategy MA50Cross: stg_ma50_*
  - Strategy M1_8_16: stg_m1_8_16_*
- Example (Phase 1 minimal set):
  - sys_risk_pct_default = 0.05
  - sys_deviation_points = 10
  - sys_filling_type = ORDER_FILLING_FOK
  - sys_default_exec_tf = PERIOD_M5
  - panel_transparency, panel_colors, panel_font_sizes
  - stg_ma50_buffer_pips = 5, stg_ma50_min_pips = 5, stg_ma50_exec_tf
  - stg_m1_8_16_tp_pips = 7, stg_m1_8_16_buffer_pips = 5, stg_m1_8_16_exec_tf


## 3) Types and Configuration (Types.mqh, Config.mqh)
- Enums
  - ENUM_STRATEGY_ID { STRAT_MA50 = 1, STRAT_M1_8_16 = 2 }
- Structs
  - StrategyConfig { id, name, enabled, risk_pct_override, exec_tf }
  - PairConfig { symbol, enabled, risk_pct_override, exec_tf, open_trades_count }
  - SystemConfig { default_risk_pct, max_open_trades, max_pairs, selected_pairs[] }
- Helpers
  - Config::Load/Save (file-based placeholder) – to persist UI changes for next session


## 4) Logger (Logger.mqh)
- Open file on OnInit, write header
- Append entries with timestamp, symbol, strategy, action, price, sl, tp, lots, result
- Close on OnDeinit
- File path: MQL5/Files/Gynsis/logs/(date).csv (ensuring portability)


## 5) Risk Management (Risk.mqh)
- Functions (Phase 1 stubs with safe defaults):
  - double CalculatePositionSize(symbol, risk_pct, sl_pips)
  - double ComputeAccumulatedRiskPctAcrossOpenTrades()
  - bool AllowNewTrade() based on accumulated risk and limits


## 6) Orders (Orders.mqh)
- Wrapper around CTrade
- SetDeviationInPoints(sys_deviation_points), SetTypeFilling(sys_filling_type)
- Helpers:
  - bool OpenBuy(symbol, lots, sl, tp, magic, comment)
  - bool OpenSell(symbol, lots, sl, tp, magic, comment)
  - bool CloseByMagic(symbol, magic)


## 7) Technical Analysis Utilities (Utils/TA.mqh)
- Placeholders returning computed values or NaN until implemented:
  - GetRecentHigh/Low
  - GetLastSwingHighLow
  - FibFromSwing
  - KeyLevels
  - Candle Patterns: isPinBar, isEngulfing (stubs)
  - Divergence checks (RSI/MACD/Stoch) – stubs
  - MA helpers: GetMA(symbol, tf, period, method)


## 8) Panel (Panel.mqh, PanelStyles.mqh)
- Use #include <Controls/Dialog.mqh> and related controls
- Create resizable dialog with tabs: Experts, Pairs, Risk, Stat, Market
- Phase 1: draw basic dialog and tab headers, layout placeholders only (no full UI logic)
- Store edits into Config via callbacks (no-op stubs returning success)


## 9) Strategy Framework
- StrategyBase.mqh: abstract base class
  - virtual string Name(); virtual int Magic(); virtual bool Enabled();
  - virtual ENUM_TIMEFRAMES ExecTF();
  - virtual void SetEnabled(bool);
  - virtual void OnInit(); virtual void OnDeinit();
  - virtual void OnTickSymbol(const string symbol); // Called by main for each enabled symbol on its configured TF sync (Phase 1 simplified)
- Strategy Implementation Placeholders
  - MA50Cross.mqh: signals on price close crossing LWMA 50; SL last swing + buffer; TP 30% to target with min pips; Trail to BE at min pips (Phase 1: only compute signal bool and log)
  - M1_8_16.mqh: signals per spec with LWMA 8/16/50/100 conditions; TP fixed pips; SL buffer (Phase 1: compute signal bool and log)
- Strategy Registry in main:
  - array of pointers/instances; iterate strategies x pairs; call OnTickSymbol with risk checks


## 10) Main Expert (Gynsis.mq5)
- #property strict
- OnInit: setup logger, config load, trade settings, instantiate strategies, setup panel
- OnTick: per tick delegate to CheckForSignals(); update panel lightweight; avoid heavy calc on every tick
- OnDeinit: save config, destroy panel, close logger
- Helper functions:
  - void CheckForSignals(); // iterate pairs and strategies, per presence rules
  - double ResolveRiskPct(symbol, strategy) per presence order (pair > strategy > system)
  - int CalculateMagic(ENUM_STRATEGY_ID id) – base + id


## 11) Presence Rules Implementation
- Resolve risk percentage in this order:
  1) Pair risk override if set (non-negative)
  2) Strategy risk override if set
  3) System default risk
- Execution timeframe in this order:
  1) Pair exec TF if not NONE
  2) Strategy default exec TF


## 12) Compilation and Debug Notes
- Use lightweight stubs returning false/0 where logic is not yet implemented
- Ensure all modules compile even if functions are empty
- Avoid blocking operations in OnTick; all IO limited to OnInit/OnDeinit and rare events
- mql_tools VSCode extension can compile – ensure no errors by using only MT5 standard headers and safe placeholders


## 13) Phase 1 Delivery Checklist
- Files scaffolded as listed
- Compiles successfully
- Panel shows with 5 tabs and basic resizable dialog
- Strategies register and log their presence (no real trading yet)
- Logger writes to file
- Config load/save placeholders present
- Orders and risk functions callable but safe (no trade open in Phase 1 unless explicitly toggled later)


## 14) Next Steps (Phase 2 Preview)
- Implement full panel interactivity (list experts/pairs, controls, persistence)
- Complete technical utilities (swings, fibs, divergences)
- Implement full strategy calculations and order placement with risk sizing
- Add API HTTP client and Telegram messaging and commands
- Add account statistics aggregation
