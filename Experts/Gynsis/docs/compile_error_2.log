[Starting] [16:28:13] Syntax checking >>> Gynsis.mq5 <<< 


null
'Trade.mqh'
'Object.mqh'
'StdLibErr.mqh'
'OrderInfo.mqh'
'HistoryOrderInfo.mqh'
'PositionInfo.mqh'
'DealInfo.mqh'
'Types.mqh'
'Globals.mqh'
'Config.mqh'
'Logger.mqh'
'Risk.mqh'
'Orders.mqh'
'PanelStyles.mqh'
'Panel.mqh'
'Storage.mqh'
'TA.mqh'
'StrategyBase.mqh'
'MA50Cross.mqh'
'M1_8_16.mqh'
🟡 : version '0.100' is incompatible with MQL5 Market, must be xxx.yyy (3,11)
🟡 : implicit conversion from 'number' to 'string' (16,28)
🟡 : implicit conversion from 'number' to 'string' (16,33)
⛔ : wrong parameters count (20,14)
 •      built-in: int iMA(const string,ENUM_TIMEFRAMES,int,int,ENUM_MA_METHOD,int)
⛔ : '>' - operand expected (110,22)
⛔ : 'OnInit' - object pointer expected (110,23)
🟡 : result of expression not used (110,21)
⛔ : '&' - reference cannot used (118,18)
⛔ : '>' - operand expected (125,18)
⛔ : undeclared identifier (125,19)
⛔ : ')' - expression expected (125,27)
🟡 : expression not boolean (125,17)
⛔ : '>' - operand expected (129,77)
⛔ : undeclared identifier (129,78)
⛔ : ')' - expression expected (129,85)
⛔ : 'ExecTF' - object pointer expected (129,78)
⛔ : 'exec_tf' - object pointer expected (129,63)
🟡 : implicit enum conversion (129,58)
⛔ : '>' - operand expected (134,14)
⛔ : undeclared identifier (134,15)
⛔ : 'symbol' - some operator expected (134,31)
⛔ : 'OnTickSymbol' - object pointer expected (134,15)
🟡 : expression has no effect (134,13)
⛔ : '>' - operand expected (150,11)
⛔ : undeclared identifier (150,12)
⛔ : ')' - expression expected (150,25)
⛔ : 'RiskOverride' - object pointer expected (150,12)
⛔ : '0' - object pointer expected (150,30)
⛔ : '>' - operand expected (151,18)
⛔ : undeclared identifier (151,19)
⛔ : ')' - expression expected (151,32)
⛔ : 'RiskOverride' - object pointer expected (151,19)
⛔ : 'return' - illegal operation use (151,7)

[Error] Result: 26 errors, 7 warnings
