# Project objectives

Build an MQL5 expert blueprint with core functionality for a trading system, including main features:


- Manage and run multiple strategies 
- Risk management on the entire account balance 
- Display a dashboard panel with multiple tabs to manage system and strategies configuration and display account statics 
- Log all trading actions into external log file in a predefined format for a later analysis usage
- Build an API connection with an external API to send and receive data
- Integrate with Telegram for sending notifications and receive orders to apply in the Metatrader platform

### Project Details
Name: Gynsis
Author: <PERSON>: Gynsis

### General code engineering rules:
- always optimize for resource management, memory leakage prevention, and CPU optimal usage
- on removing the script ensure onDeinit is removing all related chart resources and clearing memory.
- Follow DRY code principle, and Clean Code principles

## Phase 1:

Create an expert project file structure and functionality placeholder blueprints, aim to buil a clean code structure over multiple files where the main file is used for platform event and general functions, and specific strategy conditions, calculation, and panel drawing code to be in separate files for the ease of code management and following clean code and functional coding approach

- The main expert file should include other components from different files within the same project folder. 
- Each strategy will be defined in a separate folder and included in the main expert file
- Dashboard and display code will be created in a separate file and included into main expert file
- Main expert file will have the code for MT5 platform events e.g. ontick, oninit, ondieint and all other used events.
- Main file will have the code logic for checking for price signals use included files to perform the strategy checks 
- Example of main file functions checkForSignals() for enabled strategies on enabled pairs
- openPositions, ClaculatSL_With_confiugred_risks, manage_positions, manager trailing stops based on the strategy opened the trade
- Set differnt magix number and notes on each position based on strategy opened the trade






### System Utilities:
Global Technical analysis code file containing global function to use on any pair and utilized by all other functions/files
Calculate recent highs/lows level
Calculate the last swing
Calculate Fib level based on the last swing
Key levels
Price action candles
Calculate Indicatore (RSI, MACD, Stoch) divergance


### Order of presence in the system:
For the configured variable following is the order from high to low of presence:

#### Trade risk percentage
1: Pair risk_percentage value, defaulted as general system_risk_percentage, govern if different than strategy risk.
2: Expert(strategy) configured risk_percentage, defaulted as general system_risk_percentage, govern if different than expert  risk
3: the general expert system risk_percentage value, defaulted as “0.05”
#### Execution time frame:
-  1: pair configured execution_timeframe [none, 1m, 2m, 5m ,1h, 4h] ,(default 5m), none fallback to strategy execution_timeframe
-  2: strategy  execution_timeframe default is defined on each strategy file

### General instruction:
- set SetDeviationInPoints(10);
-  set SetTypeFilling(ORDER_FILLING_FOK);

A) The system panel component:
Build an interactive chart panel using MQL Controls library e.g. #include <Controls/Dialog.mqh> 
The panel is used to edit the system configuration. System configuration need to be stored for the next time user start the expert


## Panel Components:
The panel need to have multiple tabs

#### A) “Experts” Tab
- This tabe will have list of rows
- Each row is for an available expert in the system, available systems list will be hardcoded in the main expert file storing “Expert_NAME” and “EXPRT_MQL_FILE”
- Each row ui component will, Expert name, 1 button to enable/disable, float input to enter the risk percentage to be used in calculation for this expert trades (if set will override expert risk value), small number showing curren open trades by this expert, dropdown to select strategy execution_timeframe

####  B) “Pairs” Tab
- List of selected pairs the system will run the experts on, default is the current chart pair with “add” button to allow selecting other pairs from the broker available paris by typing and autocomplete input
- Each row will be for 1 pair displaying the pair name, enable/disable trading on pair button, risk percentage on pair (if set will override strategy and expert risk valu), numer of open trades on pair, current profit/loss value and percentage on all pair trades, execution_timeframe if set it will override strategy execution_timeframe
- Also need action buttons on each pair row remove pair small x icon to remove it from trading_pairs_list, “Kill” to close all open trades on the pair
- For any other trade opened manually it’s pair need to be added to the system
- At the bottom left of this tab need 3 fixed position buttons: “Kill ALL” close all open trades on all pairs, “Kill Win” Close all open wining trades, and “Kill Loss”

#### C) “Risk” 
- This tab will hold configuration for account balance risk management
- Input for risk_percentage in a single trade to be used to determine the position pips size before open the trade ( if it matches the strategy conditions of sl)
- Accumulated risk_percentage per account for all open trades, do not allow open new trades if accumulated calculated risk is higher than the configured value by 30%
- Max number of open trades at the same time
- Max number of trading pairs at the same time

#### D) “Stat”
- This tab to display account level statistics 
- Overall Balance/Equity
- Open trades count, profitable/lossing trade counts
- Open trades accumulated risk percentage value and value in money, based on each trade sl (if there is no sl count it as 0)
- Trades opened by the system count
- Manually opened trades count and their risk percentage 

#### E) “Market”
- Add place holder for showing selecte pair analysis details
- Details to be added at a later phase



	


### Panel UI and layout:
- Panel should be resizable
- Panel background and components background need to be transparent with input var to manager panel_transpercy level to allow viewing the chart behind it.
- Panel background, icons colors, text color, individual grouped buttons color and sizes need to be configurable as variable  inside - - the panel file but not in expert inputs 


## B) Strategies component:


The system is intended to support running multiple strategies on multiple selected paris based on limit and risk configurations.
Each strategy should be standalone, coded in a separate file, and not affecting or being affected by other files.
All strategies should utilize general utilities files for technical indicators to avoid redundant code and implement DRY code principle 

Strategy parameters should be available in the main expert inputs by including it for backtesting optimization.

Main expert inputs need to be organized and grouping each component or strategy set of parameters for ease of use and editing. Name of parameters should be prefixed with strategy or component name e..g stg_50ma_buffer_pips, stg_50ma_execution_tf, panel_bg_color, panel_tab1_button_enabled_bg_color, panel_tab1_button_disabled_bg_color. Panel_large_text_size, Panel_small_text_size, Panel_profitt_text_color, Panel_loss_text_color... 


For this phase let’s create 2 simple strategies

### B.1) 50MA_crossing 
Signal: On the configured time frame if price cross and close the 50 linearWMA 
SL: last high/bottom + buffer pips (buffer pips-> input variable: default: 5)
TP: reaching %30  of the recent high/low level in the direction of the trade, with min_pips value  (input_var:default:5), 
TrailingSL: move to entry point on reach  the min_pips value level

### B.2) 1M_8_16
Conditions: 
LWMA_50 is above/below LWMA_100
&& LWMA_8 above/below lwma_16 
&& LWM_8 and LWMA16 both above LWMA_50. 
Signal: price corse and close above both LWMA_8 and LWMA 16
SL: last high/bottom + buffer pips (buffer pips-> input variable: default: 5)
TP: configured TP_pips_1 (input variable: default: 7 )
