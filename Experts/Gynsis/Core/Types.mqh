#ifndef GYNSIS_CORE_TYPES_MQH
#define GYNSIS_CORE_TYPES_MQH
// Gynsis/Core/Types.mqh
// Common enums, structs, and constants shared across the EA modules

#include <Trade/Trade.mqh>

// Strategy identifiers
enum ENUM_STRATEGY_ID
  {
   STRAT_MA50        = 1,
   STRAT_M1_8_16     = 2
  };

// Magic numbers base
#define GYNSIS_MAGIC_BASE    750000
#define GYNSIS_MAGIC_MA50    (GYNSIS_MAGIC_BASE + STRAT_MA50)
#define GYNSIS_MAGIC_M1_8_16 (GYNSIS_MAGIC_BASE + STRAT_M1_8_16)

// EA identifiers
#define GYNSIS_EA_NAME    "Gynsis"
#define GYNSIS_EA_VERSION "0.100"

// Strategy configuration container
struct StrategyConfig
  {
   ENUM_STRATEGY_ID  id;
   string            name;
   bool              enabled;
   double            risk_pct_override;   // <0 => not set
   ENUM_TIMEFRAMES   exec_tf;             // PERIOD_CURRENT => not set / inherit

   void Reset()
     {
      id = (ENUM_STRATEGY_ID)0;
      name = "";
      enabled = false;
      risk_pct_override = -1.0;
      exec_tf = PERIOD_CURRENT;
     }
  };

// Per-symbol configuration
struct PairConfig
  {
   string           symbol;
   bool             enabled;
   double           risk_pct_override;  // <0 => not set
   ENUM_TIMEFRAMES  exec_tf;            // PERIOD_CURRENT => not set
   int              open_trades_count;

   void Reset()
     {
      symbol = _Symbol;
      enabled = true;
      risk_pct_override = -1.0;
      exec_tf = PERIOD_CURRENT;
      open_trades_count = 0;
     }
  };

// System-level configuration
struct SystemConfig
  {
   double                 default_risk_pct;   // default 0.05
   int                    max_open_trades;    // cap across account
   int                    max_pairs;          // cap pairs processed
   int                    deviation_points;   // e.g., 10
   ENUM_ORDER_TYPE_FILLING filling_type;      // e.g., ORDER_FILLING_FOK
   ENUM_TIMEFRAMES        default_exec_tf;    // e.g., PERIOD_M5

   // rudimentary storage for up to a small number of pairs in phase 1
   PairConfig             pairs[10];
   int                    pairs_count;

   void Reset()
     {
      default_risk_pct = 0.05;
      max_open_trades = 10;
      max_pairs = 5;
      deviation_points = 10;
      filling_type = ORDER_FILLING_FOK;
      default_exec_tf = PERIOD_M5;
      pairs_count = 0;
     }
  };

#endif // GYNSIS_CORE_TYPES_MQH
