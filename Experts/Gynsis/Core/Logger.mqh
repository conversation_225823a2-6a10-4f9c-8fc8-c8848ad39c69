#ifndef GYNSIS_CORE_LOGGER_MQH
#define GYNSIS_CORE_LOGGER_MQH
// Gynsis/Core/Logger.mqh

class Logger
  {
private:
   int      m_handle;
   string   m_file;
public:
            Logger():m_handle(INVALID_HANDLE),m_file(""){}
   bool     Open(const string base_name)
     {
      datetime now = TimeLocal();
      string date = TimeToString(now, TIME_DATE);
      StringReplace(date, ".", "-");
      m_file = base_name + "-" + date + ".csv"; // store in MQL5/Files root
      m_handle = FileOpen(m_file, FILE_WRITE|FILE_READ|FILE_TXT|FILE_CSV|FILE_SHARE_READ|FILE_SHARE_WRITE, ";");
      if(m_handle==INVALID_HANDLE)
         return(false);
      FileSeek(m_handle, 0, SEEK_END);
      if(FileSize(m_handle) == 0)
        {
         FileWrite(m_handle, "time","level","msg","extra");
        }
      return(true);
     }
   void     Close()
     {
      if(m_handle!=INVALID_HANDLE)
        {
         FileClose(m_handle);
         m_handle = INVALID_HANDLE;
        }
     }
   void     Write(const string level, const string msg, const string extra="")
     {
      if(m_handle==INVALID_HANDLE) return;
      FileWrite(m_handle, TimeToString(TimeLocal(), TIME_DATE|TIME_SECONDS), level, msg, extra);
     }
   void     Info(const string msg, const string extra="") { Write("INFO", msg, extra); }
   void     Warn(const string msg, const string extra="") { Write("WARN", msg, extra); }
   void     Error(const string msg, const string extra="") { Write("ERROR", msg, extra); }
  };

#endif // GYNSIS_CORE_LOGGER_MQH
