#ifndef GYNSIS_CORE_ORDERS_MQH
#define GYNSIS_CORE_ORDERS_MQH
// Gynsis/Core/Orders.mqh

#include <Trade/Trade.mqh>
#include "Types.mqh"

class Orders
  {
private:
   CTrade m_trade;
public:
   void Configure(const int deviation_points, const ENUM_ORDER_TYPE_FILLING filling)
     {
      m_trade.SetDeviationInPoints(deviation_points);
      m_trade.SetTypeFilling(filling);
     }

   bool OpenBuy(const string symbol, const double lots, const double sl_price, const double tp_price, const int magic, const string comment)
     {
      m_trade.SetExpertMagicNumber(magic);
      return m_trade.Buy(lots, symbol, 0.0, sl_price, tp_price, comment);
     }

   bool OpenSell(const string symbol, const double lots, const double sl_price, const double tp_price, const int magic, const string comment)
     {
      m_trade.SetExpertMagicNumber(magic);
      return m_trade.Sell(lots, symbol, 0.0, sl_price, tp_price, comment);
     }

   bool CloseByMagic(const string symbol, const int magic)
     {
      // Placeholder: iterate open positions and close by magic
      return(true);
     }
  };

#endif // GYNSIS_CORE_ORDERS_MQH
