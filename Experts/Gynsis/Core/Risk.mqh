#ifndef GYNSIS_CORE_RISK_MQH
#define GYNSIS_CORE_RISK_MQH
// Gynsis/Core/Risk.mqh

#include "Types.mqh"

namespace Risk
  {
   double CalculatePositionSize(const string symbol, const double risk_pct, const double sl_pips)
     {
      if(sl_pips<=0 || risk_pct<=0) return(0.0);
      double balance = AccountInfoDouble(ACCOUNT_BALANCE);
      double risk_money = balance * risk_pct;
      double tick_size = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
      double tick_value = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
      int digits = (int)SymbolInfoInteger(symbol, SYMBOL_DIGITS);
      double pip_in_points = (digits==3 || digits==5) ? 10.0 : 1.0;
      double sl_points = sl_pips * pip_in_points;
      double points_value_per_lot = (sl_points / tick_size) * tick_value;
      if(points_value_per_lot<=0) return(0.0);
      double lots = risk_money / points_value_per_lot;
      // Normalize to symbol lot steps
      double step = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
      double minl = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
      double maxl = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
      if(step>0)
         lots = MathFloor(lots/step)*step;
      lots = MathMax(minl, MathMin(maxl, lots));
      return(lots);
     }

   double ComputeAccumulatedRiskPctAcrossOpenTrades()
     {
      // Placeholder: iterate positions and sum (risk_at_sl / balance)
      return(0.0);
     }

   bool AllowNewTrade()
     {
      // Placeholder: compare accumulated risk with thresholds
      return(true);
     }
  }

#endif // GYNSIS_CORE_RISK_MQH
