#ifndef GYNSIS_CORE_STORAGE_MQH
#define GYNSIS_CORE_STORAGE_MQH
// Gynsis/Core/Storage.mqh

namespace Storage
  {
   bool WriteString(const string path, const string data)
     {
      int h = FileOpen(path, FILE_WRITE|FILE_TXT|FILE_SHARE_READ|FILE_SHARE_WRITE);
      if(h==INVALID_HANDLE) return(false);
      FileWriteString(h, data);
      FileClose(h);
      return(true);
     }

   bool ReadString(const string path, string &out)
     {
      int h = FileOpen(path, FILE_READ|FILE_TXT|FILE_SHARE_READ|FILE_SHARE_WRITE);
      if(h==INVALID_HANDLE) return(false);
      out = FileReadString(h, (int)FileSize(h));
      FileClose(h);
      return(true);
     }
  }

#endif // GYNSIS_CORE_STORAGE_MQH
