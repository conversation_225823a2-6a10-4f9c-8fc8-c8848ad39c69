#ifndef GYNSIS_CORE_CONFIG_MQH
#define GYNSIS_CORE_CONFIG_MQH
// Gynsis/Core/Config.mqh

#include "Types.mqh"

namespace Config
  {
   bool Load(SystemConfig &cfg)
     {
      // Placeholder: could load from file in MQL5/Files/Gynsis/config.json
      // For Phase 1, we use current inputs and defaults.
      return(true);
     }

   bool Save(const SystemConfig &cfg)
     {
      // Placeholder: persist to file for next run
      return(true);
     }
  }

#endif // GYNSIS_CORE_CONFIG_MQH
