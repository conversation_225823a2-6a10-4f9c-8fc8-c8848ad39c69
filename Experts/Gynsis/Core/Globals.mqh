#ifndef GYNSIS_CORE_GLOBALS_MQH
#define GYNSIS_CORE_GLOBALS_MQH
// Gynsis/Core/Globals.mqh
// External inputs and shared globals

#include "Types.mqh"

// General system inputs (prefix: sys_)
input double sys_risk_pct_default = 0.05;                 // default system risk pct
input int    sys_max_open_trades  = 10;                   // max open trades overall
input int    sys_max_pairs        = 5;                    // max pairs processed at once
input int    sys_deviation_points = 10;                   // SetDeviationInPoints(10)
input ENUM_ORDER_TYPE_FILLING sys_filling_type = ORDER_FILLING_FOK; // SetTypeFilling
input ENUM_TIMEFRAMES sys_default_exec_tf = PERIOD_M5;    // default timeframe

// Panel inputs (kept here for optimizer visibility, but styles in PanelStyles)
input int panel_transparency = 20;                        // 0..255 lower -> more transparent
input color panel_bg_color = clrBlack;
input color panel_text_color = clrWhite;
input color panel_accent_color = clrDodgerBlue;
input int panel_font_large = 12;
input int panel_font_small = 9;

// Strategy: MA50Cross (prefix: stg_ma50_)
input int    stg_ma50_exec_tf   = PERIOD_M5;  // default execution timeframe
input double stg_ma50_buffer_pips = 5;        // SL buffer
input int    stg_ma50_min_pips  = 5;          // min pips for TP calc & move BE

// Strategy: M1_8_16 (prefix: stg_m1_8_16_)
input int    stg_m1_8_16_exec_tf   = PERIOD_M1;
input double stg_m1_8_16_buffer_pips = 5;
input int    stg_m1_8_16_tp_pips     = 7;

// Shared runtime system configuration (populated at OnInit)
static SystemConfig G_SysConfig;

#endif // GYNSIS_CORE_GLOBALS_MQH
