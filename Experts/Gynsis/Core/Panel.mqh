#ifndef GYNSIS_CORE_PANEL_MQH
#define GYNSIS_CORE_PANEL_MQH
// Gynsis/Core/Panel.mqh
// Simplified panel implementation using MetaTrader Controls library

#include <Controls\Dialog.mqh>
#include <Controls\Panel.mqh>
#include <Controls\Label.mqh>
#include <Controls\Button.mqh>
#include "PanelStyles.mqh"
#include "Globals.mqh"

#define PANEL_WIDTH     300
#define PANEL_HEIGHT    400
#define TAB_HEIGHT      25
#define MARGIN          10

class GynsisPanel : public CAppDialog
  {
private:
   CPanel            m_main_panel;
   CButton           m_btn_experts;
   CButton           m_btn_pairs;
   CButton           m_btn_risk;
   CButton           m_btn_stat;
   CButton           m_btn_market;
   
   // Content panels
   CPanel            m_content_panel;
   CLabel            m_content_label;
   
   PanelStyle        m_style;
   bool              m_visible;
   int               m_active_tab;

public:
                     GynsisPanel();
                    ~GynsisPanel();
   
   bool              Create(const long chart_id, const string name, const int subwin = 0, const int x1 = 30, const int y1 = 30);
   void              Destroy(const int reason = REASON_PROGRAM);
   virtual bool      OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam);
   
   void              Show();
   void              Hide();
   void              Pulse();
   
protected:
   virtual bool      CreateControls();
   virtual void      UpdateContent();
   
   void              OnTabClick(int tab_index);
  };

GynsisPanel::GynsisPanel() : m_visible(false), m_active_tab(0)
  {
   m_style.FromInputs();
  }

GynsisPanel::~GynsisPanel()
  {
  }

bool GynsisPanel::Create(const long chart_id, const string name, const int subwin = 0, const int x1 = 30, const int y1 = 30)
  {
   if(!CAppDialog::Create(chart_id, name, subwin, x1, y1, x1 + PANEL_WIDTH, y1 + PANEL_HEIGHT))
      return false;
   
   // Set panel properties
   ColorBackground(m_style.bg_color);
   ColorBorder(m_style.accent_color);
   
   // Create controls
   if(!CreateControls())
      return false;
   
   m_visible = true;
   return true;
  }

void GynsisPanel::Destroy(const int reason = REASON_PROGRAM)
  {
   m_visible = false;
   CAppDialog::Destroy(reason);
  }

bool GynsisPanel::CreateControls()
  {
   // Create tab buttons
   if(!m_btn_experts.Create(m_chart_id, m_name + "_BtnExperts", m_subwin, 
                           MARGIN, MARGIN, MARGIN + 55, MARGIN + TAB_HEIGHT))
      return false;
   m_btn_experts.Text("Experts");
   m_btn_experts.ColorBackground(m_style.accent_color);
   if(!Add(m_btn_experts))
      return false;
   
   if(!m_btn_pairs.Create(m_chart_id, m_name + "_BtnPairs", m_subwin, 
                         MARGIN + 60, MARGIN, MARGIN + 110, MARGIN + TAB_HEIGHT))
      return false;
   m_btn_pairs.Text("Pairs");
   m_btn_pairs.ColorBackground(m_style.bg_color);
   if(!Add(m_btn_pairs))
      return false;
   
   if(!m_btn_risk.Create(m_chart_id, m_name + "_BtnRisk", m_subwin, 
                        MARGIN + 115, MARGIN, MARGIN + 160, MARGIN + TAB_HEIGHT))
      return false;
   m_btn_risk.Text("Risk");
   m_btn_risk.ColorBackground(m_style.bg_color);
   if(!Add(m_btn_risk))
      return false;
   
   if(!m_btn_stat.Create(m_chart_id, m_name + "_BtnStat", m_subwin, 
                        MARGIN + 165, MARGIN, MARGIN + 205, MARGIN + TAB_HEIGHT))
      return false;
   m_btn_stat.Text("Stat");
   m_btn_stat.ColorBackground(m_style.bg_color);
   if(!Add(m_btn_stat))
      return false;
   
   if(!m_btn_market.Create(m_chart_id, m_name + "_BtnMarket", m_subwin, 
                          MARGIN + 210, MARGIN, MARGIN + 260, MARGIN + TAB_HEIGHT))
      return false;
   m_btn_market.Text("Market");
   m_btn_market.ColorBackground(m_style.bg_color);
   if(!Add(m_btn_market))
      return false;
   
   // Create content panel
   if(!m_content_panel.Create(m_chart_id, m_name + "_ContentPanel", m_subwin, 
                             MARGIN, MARGIN + TAB_HEIGHT + 5, 
                             PANEL_WIDTH - MARGIN, PANEL_HEIGHT - MARGIN))
      return false;
   m_content_panel.ColorBackground(m_style.bg_color);
   if(!Add(m_content_panel))
      return false;
   
   // Create content label
   if(!m_content_label.Create(m_chart_id, m_name + "_ContentLabel", m_subwin, 
                             MARGIN + 10, MARGIN + TAB_HEIGHT + 15, 
                             PANEL_WIDTH - MARGIN - 10, MARGIN + TAB_HEIGHT + 45))
      return false;
   m_content_label.Text("Strategy Management");
   m_content_label.Color(m_style.text_color);
   m_content_label.FontSize(m_style.font_large);
   if(!Add(m_content_label))
      return false;
   
   return true;
  }

void GynsisPanel::Show()
  {
   m_visible = true;
   CAppDialog::Show();
  }

void GynsisPanel::Hide()
  {
   m_visible = false;
   CAppDialog::Hide();
  }

void GynsisPanel::Pulse()
  {
   if(m_visible)
     {
      UpdateContent();
     }
  }

void GynsisPanel::UpdateContent()
  {
   // Update dynamic content based on active tab
   switch(m_active_tab)
     {
      case 0: m_content_label.Text("Strategy Management"); break;
      case 1: m_content_label.Text("Currency Pairs"); break;
      case 2: m_content_label.Text("Risk Management"); break;
      case 3: m_content_label.Text("Statistics"); break;
      case 4: m_content_label.Text("Market Info"); break;
     }
  }

bool GynsisPanel::OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
  {
   if(id == CHARTEVENT_OBJECT_CLICK)
     {
      if(sparam == m_btn_experts.Name()) { OnTabClick(0); return true; }
      if(sparam == m_btn_pairs.Name()) { OnTabClick(1); return true; }
      if(sparam == m_btn_risk.Name()) { OnTabClick(2); return true; }
      if(sparam == m_btn_stat.Name()) { OnTabClick(3); return true; }
      if(sparam == m_btn_market.Name()) { OnTabClick(4); return true; }
     }
   
   return CAppDialog::OnEvent(id, lparam, dparam, sparam);
  }

void GynsisPanel::OnTabClick(int tab_index)
  {
   // Reset all button colors
   m_btn_experts.ColorBackground(m_style.bg_color);
   m_btn_pairs.ColorBackground(m_style.bg_color);
   m_btn_risk.ColorBackground(m_style.bg_color);
   m_btn_stat.ColorBackground(m_style.bg_color);
   m_btn_market.ColorBackground(m_style.bg_color);
   
   // Highlight active button
   m_active_tab = tab_index;
   switch(tab_index)
     {
      case 0: m_btn_experts.ColorBackground(m_style.accent_color); break;
      case 1: m_btn_pairs.ColorBackground(m_style.accent_color); break;
      case 2: m_btn_risk.ColorBackground(m_style.accent_color); break;
      case 3: m_btn_stat.ColorBackground(m_style.accent_color); break;
      case 4: m_btn_market.ColorBackground(m_style.accent_color); break;
     }
   
   UpdateContent();
  }

// Simple wrapper class for compatibility with existing code
class Panel
  {
private:
   GynsisPanel  m_panel;
   bool         m_visible;

public:
                Panel() : m_visible(false) {}
   bool         Create(const long chart_id, const string name)
     {
      if(m_panel.Create(chart_id, name))
        {
         m_visible = true;
         return true;
        }
      return false;
     }
   void         Show()
     {
      m_panel.Show();
      m_visible = true;
     }
   void         Destroy()
     {
      m_panel.Destroy();
      m_visible = false;
     }
   void         Pulse()
     {
      if(m_visible)
         m_panel.Pulse();
     }
   bool         OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
     {
      if(m_visible)
         return m_panel.OnEvent(id, lparam, dparam, sparam);
      return false;
     }
  };

#endif // GYNSIS_CORE_PANEL_MQH
