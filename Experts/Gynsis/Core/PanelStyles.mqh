#ifndef GYNSIS_CORE_PANEL_STYLES_MQH
#define GYNSIS_CORE_PANEL_STYLES_MQH
// Gynsis/Core/PanelStyles.mqh

struct PanelStyle
  {
   color bg_color;
   color text_color;
   color accent_color;
   int   alpha; // transparency 0..255
   int   font_large;
   int   font_small;

   void FromInputs()
     {
      bg_color = panel_bg_color;
      text_color = panel_text_color;
      accent_color = panel_accent_color;
      alpha = panel_transparency;
      font_large = panel_font_large;
      font_small = panel_font_small;
     }
  };

#endif // GYNSIS_CORE_PANEL_STYLES_MQH
