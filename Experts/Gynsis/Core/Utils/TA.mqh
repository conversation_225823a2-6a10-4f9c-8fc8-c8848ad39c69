#ifndef GYNSIS_CORE_UTILS_TA_MQH
#define GYNSIS_CORE_UTILS_TA_MQH
// Gynsis/Core/Utils/TA.mqh

namespace TA
  {
   double GetRecentHigh(const string symbol, const ENUM_TIMEFRAMES tf, const int bars_back)
     {
      int idx = iHighest(symbol, tf, MODE_HIGH, bars_back, 0);
      return(idx>=0 ? iHigh(symbol, tf, idx) : EMPTY_VALUE);
     }
   double GetRecentLow(const string symbol, const ENUM_TIMEFRAMES tf, const int bars_back)
     {
      int idx = iLowest(symbol, tf, MODE_LOW, bars_back, 0);
      return(idx>=0 ? iLow(symbol, tf, idx) : EMPTY_VALUE);
     }

   double MA(const string symbol, const ENUM_TIMEFRAMES tf, const int period, const int shift=0, const ENUM_MA_METHOD method=MODE_LWMA, const int applied=PRICE_CLOSE)
     {
      return(iMA(symbol, tf, period, 0, method, shift));
     }

   bool IsCrossCloseAboveMA(const string symbol, const ENUM_TIMEFRAMES tf, const int ma_period)
     {
      double prev_close = iClose(symbol, tf, 1);
      double curr_close = iClose(symbol, tf, 0);
      double ma_prev = MA(symbol, tf, ma_period, 1);
      double ma_curr = MA(symbol, tf, ma_period, 0);
      return(prev_close < ma_prev && curr_close > ma_curr);
     }

   bool IsCrossCloseBelowMA(const string symbol, const ENUM_TIMEFRAMES tf, const int ma_period)
     {
      double prev_close = iClose(symbol, tf, 1);
      double curr_close = iClose(symbol, tf, 0);
      double ma_prev = MA(symbol, tf, ma_period, 1);
      double ma_curr = MA(symbol, tf, ma_period, 0);
      return(prev_close > ma_prev && curr_close < ma_curr);
     }

   // Stubs for future utilities
   bool IsPinBar(const string symbol, const ENUM_TIMEFRAMES tf, const int shift=0){ return(false);} 
   bool IsEngulfing(const string symbol, const ENUM_TIMEFRAMES tf, const int shift=0){ return(false);} 
  }

#endif // GYNSIS_CORE_UTILS_TA_MQH
