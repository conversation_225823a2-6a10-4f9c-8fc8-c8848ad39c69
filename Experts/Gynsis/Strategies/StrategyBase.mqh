#ifndef GYNSIS_STRATEGIES_BASE_MQH
#define GYNSIS_STRATEGIES_BASE_MQH
// Gynsis/Strategies/StrategyBase.mqh

#include "../Core/Types.mqh"
#include "../Core/Globals.mqh"
#include "../Core/Logger.mqh"
#include "../Core/Utils/TA.mqh"

class StrategyBase
  {
protected:
   string           m_name;
   ENUM_STRATEGY_ID m_id;
   bool             m_enabled;
   ENUM_TIMEFRAMES  m_exec_tf;
   double           m_risk_override; // <0 not set
public:
                     StrategyBase():m_name(""),m_id((ENUM_STRATEGY_ID)0),m_enabled(true),m_exec_tf(PERIOD_CURRENT),m_risk_override(-1.0){}
   virtual          ~StrategyBase(){}
   virtual void      OnInit(){}
   virtual void      OnDeinit(){}
   virtual void      OnTickSymbol(const string symbol) = 0;

   string            Name() const { return m_name; }
   ENUM_STRATEGY_ID  Id() const { return m_id; }
   bool              Enabled() const { return m_enabled; }
   ENUM_TIMEFRAMES   ExecTF() const { return m_exec_tf; }
   double            RiskOverride() const { return m_risk_override; }
   void              SetEnabled(const bool e) { m_enabled = e; }
  };

#endif // GYNSIS_STRATEGIES_BASE_MQH
