#ifndef GYNSIS_STRATEGIES_MA50CROSS_MQH
#define GYNSIS_STRATEGIES_MA50CROSS_MQH
// Gynsis/Strategies/MA50Cross.mqh

#include "StrategyBase.mqh"

class MA50Cross : public StrategyBase
  {
private:
   int    m_min_pips;
   int    m_buffer_pips;
public:
            MA50Cross()
              {
               m_name = "50MA_crossing";
               m_id = STRAT_MA50;
               m_enabled = true;
               m_exec_tf = (ENUM_TIMEFRAMES)stg_ma50_exec_tf;
               m_buffer_pips = (int)stg_ma50_buffer_pips;
               m_min_pips = (int)stg_ma50_min_pips;
              }
   void      Configure(const ENUM_TIMEFRAMES tf, const int buffer_pips, const int min_pips)
              {
               m_exec_tf = tf;
               m_buffer_pips = buffer_pips;
               m_min_pips = min_pips;
              }
   virtual void OnTickSymbol(const string symbol)
              {
               // Phase 1: compute signal and log only
               bool cross_up = TA::IsCrossCloseAboveMA(symbol, m_exec_tf, 50);
               bool cross_dn = TA::IsCrossCloseBelowMA(symbol, m_exec_tf, 50);
               if(cross_up || cross_dn)
                 {
                  PrintFormat("[%s] Signal detected on %s: %s", m_name, symbol, cross_up?"BUY":"SELL");
                 }
              }
  };

#endif // GYNSIS_STRATEGIES_MA50CROSS_MQH
