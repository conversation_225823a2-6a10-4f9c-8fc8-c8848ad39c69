#ifndef GYNSIS_STRATEGIES_M1_8_16_MQH
#define GYNSIS_STRATEGIES_M1_8_16_MQH
// Gynsis/Strategies/M1_8_16.mqh

#include "StrategyBase.mqh"

class M1_8_16 : public StrategyBase
  {
private:
   int m_tp_pips;
   int m_buffer_pips;
public:
            M1_8_16()
              {
               m_name = "1M_8_16";
               m_id = STRAT_M1_8_16;
               m_enabled = true;
               m_exec_tf = (ENUM_TIMEFRAMES)stg_m1_8_16_exec_tf;
               m_buffer_pips = (int)stg_m1_8_16_buffer_pips;
               m_tp_pips = (int)stg_m1_8_16_tp_pips;
              }
   void      Configure(const ENUM_TIMEFRAMES tf, const int buffer_pips, const int tp_pips)
              {
               m_exec_tf = tf;
               m_buffer_pips = buffer_pips;
               m_tp_pips = tp_pips;
              }
   virtual void OnTickSymbol(const string symbol)
              {
               // LWMA relationships
               double ma8  = TA::MA(symbol, m_exec_tf, 8);
               double ma16 = TA::MA(symbol, m_exec_tf, 16);
               double ma50 = TA::MA(symbol, m_exec_tf, 50);
               double ma100= TA::MA(symbol, m_exec_tf, 100);

               bool above_50_100 = (ma50>ma100);
               bool below_50_100 = (ma50<ma100);
               bool fast_above_slow = (ma8>ma16);
               bool fast_below_slow = (ma8<ma16);
               bool both_above_50 = (ma8>ma50 && ma16>ma50);
               bool both_below_50 = (ma8<ma50 && ma16<ma50);

               double close0 = iClose(symbol, m_exec_tf, 0);
               double close1 = iClose(symbol, m_exec_tf, 1);
               bool cross_up = (close1<ma8 && close1<ma16 && close0>ma8 && close0>ma16);
               bool cross_dn = (close1>ma8 && close1>ma16 && close0<ma8 && close0<ma16);

               bool long_cond  = above_50_100 && fast_above_slow && both_above_50 && cross_up;
               bool short_cond = below_50_100 && fast_below_slow && both_below_50 && cross_dn;

               if(long_cond || short_cond)
                 PrintFormat("[%s] Signal on %s: %s", m_name, symbol, long_cond?"BUY":"SELL");
              }
  };

#endif // GYNSIS_STRATEGIES_M1_8_16_MQH
