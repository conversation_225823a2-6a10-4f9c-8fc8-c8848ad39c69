#property strict
#property copyright "<PERSON>"
#property version   "1.00"
#property description "Gynsis — Modular multi-strategy expert blueprint"

#include <Trade/Trade.mqh>

#include "Core/Types.mqh"
#include "Core/Globals.mqh"
#include "Core/Config.mqh"
#include "Core/Logger.mqh"
#include "Core/Risk.mqh"
#include "Core/Orders.mqh"
#include "Core/PanelStyles.mqh"
#include "Core/Panel.mqh"
#include "Core/Storage.mqh"
#include "Core/Utils/TA.mqh"
#include "Strategies/StrategyBase.mqh"
#include "Strategies/MA50Cross.mqh"
#include "Strategies/M1_8_16.mqh"

// Globals
CTrade          trade;
Logger          g_logger;
Panel           g_panel;
StrategyBase   *g_strategies[4];
int             g_strats_count = 0;

// Forward declarations
void InitConfig();
void InitStrategies();
void CheckForSignals();
double ResolveRiskPct(const string symbol, StrategyBase *stg);
int CalculateMagic(ENUM_STRATEGY_ID id);

int OnInit()
  {
   // Build runtime config from inputs
   InitConfig();

   // Logger
   g_logger.Open("Gynsis");
   g_logger.Info("EA Init", StringFormat("Version=%s", GYNSIS_EA_VERSION));

   // Trade settings
   trade.SetDeviationInPoints(sys_deviation_points);
   trade.SetTypeFilling(sys_filling_type);

   // Strategies
   InitStrategies();

   // Panel
   g_panel.Create(0, "GynsisPanel");
   g_panel.Show();

   return(INIT_SUCCEEDED);
  }

void OnDeinit(const int reason)
  {
   // Panel cleanup
   g_panel.Destroy();

   // Save config (placeholder)
   Config::Save(G_SysConfig);

   // Logger close
   g_logger.Info("EA Deinit", IntegerToString(reason));
   g_logger.Close();
  }

void OnTick()
  {
   // lightweight tick routing
   CheckForSignals();
   g_panel.Pulse();
  }

void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
  {
   // Forward chart events to the panel for proper GUI functionality
   g_panel.OnEvent(id, lparam, dparam, sparam);
  }

void InitConfig()
  {
   G_SysConfig.Reset();
   G_SysConfig.default_risk_pct = sys_risk_pct_default;
   G_SysConfig.max_open_trades = sys_max_open_trades;
   G_SysConfig.max_pairs = sys_max_pairs;
   G_SysConfig.deviation_points = sys_deviation_points;
   G_SysConfig.filling_type = sys_filling_type;
   G_SysConfig.default_exec_tf = (ENUM_TIMEFRAMES)sys_default_exec_tf;

   // initialize pairs with current symbol as default
   G_SysConfig.pairs_count = 1;
   G_SysConfig.pairs[0].Reset();
   G_SysConfig.pairs[0].symbol = _Symbol;
   G_SysConfig.pairs[0].enabled = true;
  }

void InitStrategies()
  {
   g_strats_count = 0;

   static MA50Cross s_ma50;
   static M1_8_16   s_m116;

   s_ma50.Configure((ENUM_TIMEFRAMES)stg_ma50_exec_tf, (int)stg_ma50_buffer_pips, (int)stg_ma50_min_pips);
   s_m116.Configure((ENUM_TIMEFRAMES)stg_m1_8_16_exec_tf, (int)stg_m1_8_16_buffer_pips, (int)stg_m1_8_16_tp_pips);

   g_strategies[g_strats_count++] = &s_ma50;
   g_strategies[g_strats_count++] = &s_m116;

   for(int i=0; i<g_strats_count; i++)
     g_strategies[i].OnInit();
  }

void CheckForSignals()
  {
   // Iterate pairs
   for(int p=0; p<G_SysConfig.pairs_count; ++p)
     {
      PairConfig pc = G_SysConfig.pairs[p];
      if(!pc.enabled) continue;

      // Iterate strategies
      for(int i=0; i<g_strats_count; ++i)
        {
         StrategyBase *stg = g_strategies[i];
         if(!stg.Enabled())
            continue;

         // Resolve TF according to presence rules (simplified in Phase 1)
         ENUM_TIMEFRAMES tf = pc.exec_tf!=PERIOD_CURRENT ? pc.exec_tf : stg.ExecTF();
         if(tf==PERIOD_CURRENT)
            tf = G_SysConfig.default_exec_tf;

         // In Phase 1, strategies handle their TF internally, just call
         stg.OnTickSymbol(pc.symbol);
        }
     }
  }

double ResolveRiskPct(const string symbol, StrategyBase *stg)
  {
   // presence rules: pair > strategy > system
   for(int p=0; p<G_SysConfig.pairs_count; ++p)
     if(G_SysConfig.pairs[p].symbol == symbol)
       {
        if(G_SysConfig.pairs[p].risk_pct_override >= 0)
           return G_SysConfig.pairs[p].risk_pct_override;
        break;
       }

   if(stg.RiskOverride() >= 0)
      return stg.RiskOverride();

   return G_SysConfig.default_risk_pct;
  }

int CalculateMagic(ENUM_STRATEGY_ID id)
  {
   switch(id)
     {
      case STRAT_MA50:    return GYNSIS_MAGIC_MA50;
      case STRAT_M1_8_16: return GYNSIS_MAGIC_M1_8_16;
      default:            return GYNSIS_MAGIC_BASE + (int)id;
     }
  }